["tests/test_agentic_integration.py::TestAgentContext::test_agent_context_creation", "tests/test_agentic_integration.py::TestAgenticCoder::test_agentic_coder_auto_approve", "tests/test_agentic_integration.py::TestAgenticCoder::test_agentic_coder_creation", "tests/test_agentic_integration.py::TestAgenticCoder::test_agentic_workflow_basic", "tests/test_agentic_integration.py::TestAgenticCoder::test_fallback_to_normal_mode", "tests/test_agentic_integration.py::TestIntegrationWithBaseCoder::test_agentic_mode_parameter", "tests/test_agentic_integration.py::TestIntegrationWithBaseCoder::test_get_agentic_coder_method", "tests/test_agentic_integration.py::TestTaskManager::test_create_task", "tests/test_agentic_integration.py::TestTaskManager::test_progress_summary", "tests/test_agentic_integration.py::TestTaskManager::test_serialization", "tests/test_agentic_integration.py::TestTaskManager::test_task_dependencies"]